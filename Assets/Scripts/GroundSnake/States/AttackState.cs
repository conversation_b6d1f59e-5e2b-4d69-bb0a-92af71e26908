
using System.Collections;
using System.Threading.Tasks;
using UnityEngine;

namespace GroundSnake
{
  public class AttackState : State
  {
    private Vector3 aimPosition;

    private TweenClip eatClip;
    private TweenClip aimClip;
    private TweenClip mouthOpenClip;
    private TweenClip attackClip;
    private TweenClip attackMouthOpenClip;

    private Coroutine attackCoroutine;

    public AttackState(StateMachine stateMachine) : base(stateMachine)
    {
      eatClip = Tween.CreateAnimTrackClip(stateMachine.brain.anim_eat, 1);
      aimClip = Tween.CreateAnimTrackClip(stateMachine.brain.anim_aim, 0, crossDuration: 0.5f);
      mouthOpenClip = Tween.CreateAnimTrackClip(stateMachine.brain.anim_mouthOpen, 1, crossDuration: 0.5f);
      attackMouthOpenClip = Tween.CreateAnimTrackClip(stateMachine.brain.anim_mouthOpenMax, 1);
    }

    public override void Enter(State prevState)
    {
      base.Enter(prevState);
      attackCoroutine = brain.StartCoroutine(Attack());
    }

    public override void Exit()
    {
      base.Exit();
      brain.StopCoroutine(attackCoroutine);
    }

    IEnumerator Attack()
    {
      while (brain.IsInAttackArea)
      {
        tween
          .SetTrack(t => t.anim, aimClip)
          .SetTrack(t => t.anim, mouthOpenClip);

        yield return tween.WaitReachTarget(t => t.anim, t => t.lookDirection);

        aimPosition = brain.target.transform.position;
        var constrain = new ChainMoveConstrain(brain.transform, new() { effector = brain.mouth, chainLength = 3 });
        var (fromAnimData, toAnimData) = constrain.MoveTo(brain.target.transform.position);

        attackClip = Tween.CreateAnimTrackClip(toAnimData, 0, crossDuration: brain.anim_mouthOpenMax.duration);

        tween
          .PauseTrack(t => t.lookDirection)
          .SetTrack(t => t.anim, Tween.CreateAnimTrackClip(fromAnimData, 0))
          .SetTrack(t => t.anim, attackClip)
          .SetTrack(t => t.anim, attackMouthOpenClip);

        yield return new WaitUntil(() => Vector2.SqrMagnitude(aimPosition - brain.mouth.position) < brain.eatDetectSqrDistance);
        yield return tween.WaitTrack(t => t.anim, eatClip);
        yield return tween.WaitTrack(t => t.anim, Tween.CreateAnimTrackClip(fromAnimData, 0, crossDuration: brain.anim_mouthOpenMax.duration * 2f));

        tween.ResumeTrack(t => t.lookDirection);
        yield return new WaitForSeconds(brain.attackRate);
      }

      stateMachine.TransitionTo(stateMachine.idleState);
    }

    public override void FrameUpdate()
    {
    }
  }
}