using UnityEngine;


public class ButterflyMovement : MonoBehaviour
{
  // public Transform start;
  public Transform target;
  public float speed = 5f;
  public float frequency = 2f;
  private TweenAnim<Feature> _tween;

  private Vector3 initialPosition;

  struct Feature
  {
    public Vector3 position;
    public AnimFeature anim;
  }

  void DoFeature(Feature f)
  {
    // 处理特征
    // Debug.Log(feature.feature);
    // transform.AnimFeature(f.anim);
    transform.position = f.position;
  }

  void Awake()
  {
    initialPosition = transform.position;
    _tween = gameObject.TweenAnim<Feature>();
  }

  void Start()
  {
    _tween
      .SetOnChange(DoFeature)
      .Start();
  }

  void Update()
  {
    if (Input.GetKeyDown(KeyCode.Space))
    {
      _tween
        .SetTrack(f => f.position, Tween.CreateSpeedTrackClip(
          () => target.position,
          () => speed,
          () => initialPosition,
          interpolateFunc: (current, target, speed) => {
            var pos = Vector3.MoveTowards(current, target, speed * Time.deltaTime);
            if (Mathf.Abs(pos.sqrMagnitude - target.sqrMagnitude) > 0.1f)
            {
              // Debug.Log(Mathf.PerlinNoise(0, Time.time * 0.5f));
              var x = Mathf.PerlinNoise(0, Time.time * 2f);
              pos.y += MathfExt.Remap(
                x, 0, 1, -0.01f, 0.01f);
            }
            return pos;
          }
        ));
    }
  }
}

// public class ButterflyMovement : MonoBehaviour
// {
//     public float speed = 2f;
//     public float flutterAmount = 0.5f;
//     public float directionChangeInterval = 3f;
//     public float smoothness = 2f;
    
//     private Vector2 targetDirection;
//     private float timeSinceDirectionChange;
//     private float flutterTime;
//     private float perlinOffset;

//     void Start()
//     {
//         perlinOffset = Random.Range(0f, 100f);
//         SetNewRandomDirection();
//     }

//     void Update()
//     {
//         timeSinceDirectionChange += Time.deltaTime;
//         flutterTime += Time.deltaTime * 10f; // 更快的扇翅频率
        
//         // 定期改变主要方向
//         if (timeSinceDirectionChange > directionChangeInterval)
//         {
//             timeSinceDirectionChange = 0;
//             SetNewRandomDirection();
//         }

//         // 使用Perlin噪声添加自然波动
//         float perlinX = Mathf.PerlinNoise(perlinOffset + Time.time * 0.5f, 0) * 2 - 1;
//         float perlinY = Mathf.PerlinNoise(0, perlinOffset + Time.time * 0.5f) * 2 - 1;
//         Vector2 perlinOffsetVec = new Vector2(perlinX, perlinY) * flutterAmount;
        
//         // 添加扇翅效果 (上下波动)
//         float flutterY = Mathf.Sin(flutterTime) * 0.2f;
        
//         // 组合所有运动因素
//         Vector2 finalDirection = Vector2.Lerp(transform.up, targetDirection, Time.deltaTime * smoothness);
//         transform.up = finalDirection;
        
//         transform.position += (Vector3)(finalDirection * speed + perlinOffsetVec + Vector2.up * flutterY) * Time.deltaTime;
//     }

//     void SetNewRandomDirection()
//     {
//         targetDirection = new Vector2(Random.Range(-1f, 1f), Random.Range(-0.5f, 1f)).normalized;
//     }
// }