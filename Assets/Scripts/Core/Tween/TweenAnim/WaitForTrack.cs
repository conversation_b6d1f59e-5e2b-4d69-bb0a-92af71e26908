
using System;
using System.Linq;
using System.Linq.Expressions;
using UnityEngine;

public class WaitForTweenTrack<T> : CustomYieldInstruction where T : new()
{
  private TweenTrack<T>[] _tracks;

  public WaitForTweenTrack(TweenAnim<T> tween, Expression<Func<T, object>>[] propertySelectors)
  {
    _tracks = propertySelectors.Select(p => tween.GetTrack(p)).ToArray();
  }
  public WaitForTweenTrack(TweenAnim<T> tween, string[] fieldNames)
  {
    _tracks = fieldNames.Select(p => tween.GetTrack(p)).ToArray();
  }

  public override bool keepWaiting
  {
    get
    {
      return _tracks.Any(t => !t.<PERSON>alid || !t.IsReachTarget);
    }
  }
}
