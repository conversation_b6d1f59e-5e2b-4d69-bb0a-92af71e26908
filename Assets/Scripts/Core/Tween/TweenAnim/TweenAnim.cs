
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using UnityEngine;

public class TweenTrackBatchChange
{
  public float? duration = null;
  public float? speed = null;
  public bool? reverse = null;
  public int? loopCount = null;
  public bool? pingpong = null;
  public EaseType? easeType = null;
  public Func<float, float> easeFunc = null;
  public AnimationCurve curve = null;
  public bool? completable = null;
  public float? crossDuration;
  public EaseType? crossEaseType = null;
  public Func<float, float> crossEaseFunc = null;
  public AnimationCurve crossCurve = null;

}

public class TweenAnim<T> : TweenTimeGeneral<TweenAnim<T>> where T : new()
{
  public delegate void TrackCompleteCallback(TweenTrack<T> tweenTrack);

  private Dictionary<string, TweenTrack<T>> _tweenTracks = new();
  public ObservableData<T> Data;

  private HashSet<(Action callback, string[] fieldNames)> _reachCallbacks = new();

  TweenTrackBatchChange _batchChange = null;

  public TweenAnim(object target, string identifier, T defaultValue, TweenTrack<T>[] tweenTracks, bool completable) : base(target, identifier, completable)
  {
    Data = new ObservableData<T>(defaultValue);

    foreach (var tweenTrack in tweenTracks)
    {
      _tweenTracks.Add(tweenTrack.fieldName, tweenTrack);
    }
  }

  public T DefaultValue => Data.DefaultValue;
  public T Value => Data.Value;
  public event Action<T> ValueChanged
  {
    add => Data.valueChanged += value;
    remove => Data.valueChanged -= value;
  }

  // protected override float Duration
  // {
  //   get => _tweenTracks.Values.MaxBy(t => t.TotalDuration);
  // }

  public float ElapsedTime => _elapsedTime;
  public bool Reverse => _reverse;

  public int TrackCount => _tweenTracks.Count;

  public override void Kill()
  {
    base.Kill();
  }
  public override void Rewind()
  {
    base.Rewind();
    foreach (var tweenTrack in _tweenTracks.Values)
    {
      tweenTrack.Rewind();
    }
  }

  private void AddTrack(TweenTrack<T> tweenTrack)
  {
    if (_tweenTracks.ContainsKey(tweenTrack.fieldName))
    {
      _tweenTracks.Remove(tweenTrack.fieldName);
    }

    _tweenTracks.Add(tweenTrack.fieldName, tweenTrack);
  }

  public TweenTrack<T> GetTrack(string fieldName)
  {
    if (!_tweenTracks.ContainsKey(fieldName))
      return default;

    return _tweenTracks[fieldName];
  }

  public bool HasTrack<P>(Expression<Func<T, P>> propertySelector)
  {
    return GetTrack(propertySelector) != null;
  }
  public bool IsTrackRunning<P>(Expression<Func<T, P>> propertySelector)
  {
    var track = GetTrack(propertySelector);
    return track != null && !track.IsPaused;
  }
  public bool IsTrackReachTarget<P>(Expression<Func<T, P>> propertySelector)
  {
    var track = GetTrack(propertySelector);
    return track == null || track.IsReachTarget;
  }

  public TweenAnimSubTrack<T> GetSubAnimTrack<P>(Expression<Func<T, P>> propertySelector, int layerIndex = 0)
  {
    var track = GetTrack(propertySelector);
    if (track == null || track is not TweenAnimTrack<T> animTrack)
    {
      Debug.LogWarning($"No track found for property: {GetFieldName(propertySelector)}");
      return null;
    }

    var subTrack = animTrack.GetTrack(layerIndex);
    if (subTrack == null)
    {
      Debug.LogWarning($"No subtrack found for layer index: {layerIndex} in property: {GetFieldName(propertySelector)}");
      return null;
    }

    return subTrack;
  }

  public TweenTrack<T> GetTrack<P>(Expression<Func<T, P>> propertySelector)
  {
    return GetTrack(GetFieldName(propertySelector));
  }

  public TweenTimeTrack<T, P> GetTimeTrack<P>(Expression<Func<T, P>> propertySelector)
  {
    return (TweenTimeTrack<T, P>)GetTrack(propertySelector);
  }
  public TweenSpeedTrack<T, P> GetSpeedTrack<P>(Expression<Func<T, P>> propertySelector)
  {
    return (TweenSpeedTrack<T, P>)GetTrack(propertySelector);
  }
  public TweenFreeAnimTrack<T> GetFreeAnimTrack<P>(Expression<Func<T, P>> propertySelector)
  {
    return (TweenFreeAnimTrack<T>)GetTrack(propertySelector);
  }
  public TweenAnimSubTrack<T> GetAnimTrack<P>(Expression<Func<T, P>> propertySelector, int layerIndex)
  {
    var animTrack = (TweenAnimTrack<T>)GetTrack(propertySelector);
    return animTrack.GetTrack(layerIndex);
  }

  private TweenClip ApplyBatchChange<P>(TweenClip change)
  {
    if (_batchChange != null)
    {
      if (change is TweenTimeTrackClip<P> timeTrackChange)
      {
        timeTrackChange.duration ??= _batchChange.duration;
        timeTrackChange.speed ??= _batchChange.speed;
        timeTrackChange.reverse ??= _batchChange.reverse;
        timeTrackChange.loopCount ??= _batchChange.loopCount;
        timeTrackChange.pingpong ??= _batchChange.pingpong;
        timeTrackChange.easeType ??= _batchChange.easeType;
        timeTrackChange.easeFunc ??= _batchChange.easeFunc;
        timeTrackChange.curve ??= _batchChange.curve;
        timeTrackChange.crossDuration ??= _batchChange.crossDuration;
        timeTrackChange.crossEaseType ??= _batchChange.crossEaseType;
        timeTrackChange.crossEaseFunc ??= _batchChange.crossEaseFunc;
        timeTrackChange.crossCurve ??= _batchChange.crossCurve;
        return timeTrackChange;
      }
      else if (change is TweenSpeedTrackClip<P> speedTrackChange)
      {
        speedTrackChange.completable ??= _batchChange.completable;
        return speedTrackChange;
      }
      else if (change is TweenAnimSubTrackClip animTrackChange)
      {
        animTrackChange.crossDuration ??= _batchChange.crossDuration;
        animTrackChange.crossEaseType ??= _batchChange.crossEaseType;
        animTrackChange.crossCurve ??= _batchChange.crossCurve;
        animTrackChange.crossEaseFunc ??= _batchChange.crossEaseFunc;
        animTrackChange.reverse ??= _batchChange.reverse;
        animTrackChange.speed ??= _batchChange.speed;
        animTrackChange.loopCount ??= _batchChange.loopCount;
        animTrackChange.pingpong ??= _batchChange.pingpong;
        return animTrackChange;
      }
      // else if (change is TweenFreeAnimTrackChange freeAnimTrackChange)
      // {

      // }
    }
    return change;
  }

  private string GetFieldName<P>(Expression<Func<T, P>> propertySelector)
  {
    var body = propertySelector.Body;
    if (body is UnaryExpression unary && unary.NodeType == ExpressionType.Convert)
    {
      body = unary.Operand;
    }

    if (body is MemberExpression member)
    {
      return member.Member.Name;
    }

    throw new ArgumentException("error format");

  }

  private TT AddGenericTrack<TT, P>(string fieldName, TweenClip clip) where TT : TweenTrack<T>
  {
    if (clip == null)
    {
      if (_tweenTracks.ContainsKey(fieldName))
        _tweenTracks.Remove(fieldName);
      return null;
    }

    var track = GetTrack(fieldName);

    clip = ApplyBatchChange<P>(clip);

    IsComplete = false;

    if (track is TT tTrack)
    {
      tTrack.Change(clip);
      return tTrack;
    }

    var newTrack = (TT)Activator.CreateInstance(typeof(TT), this, fieldName);
    AddTrack(newTrack);
    newTrack.Change(clip);
    return newTrack;
  }
  public TweenTrack<T> AddTrack<P>(string fieldName, TweenClip clip)
  {
    if (clip is TweenTimeTrackClip<P>)
    {
      return AddGenericTrack<TweenTimeTrack<T, P>, P>(fieldName, clip);
    }
    else if (clip is TweenSpeedTrackClip<P>)
    {
      return AddGenericTrack<TweenSpeedTrack<T, P>, P>(fieldName, clip);
    }
    else if (clip is TweenFreeAnimTrackClip)
    {
      return AddGenericTrack<TweenFreeAnimTrack<T>, P>(fieldName, clip);
    }
    else if (clip is TweenAnimSubTrackClip)
    {
      return AddGenericTrack<TweenAnimTrack<T>, P>(fieldName, clip);
    }
    throw new NotImplementedException();
  }
  public TweenTrack<T> AddTrack<P>(Expression<Func<T, P>> propertySelector, TweenClip clip)
  {
    return AddTrack<P>(GetFieldName(propertySelector), clip);
  }

  public TweenAnim<T> SetTrack<P>(Expression<Func<T, P>> propertySelector, TweenClip clip)
  {
    AddTrack(propertySelector, clip);
    return this;
  }

  public System.Collections.IEnumerator WaitTrack<P>(Expression<Func<T, P>> propertySelector, TweenClip clip)
  {
    var fieldName = GetFieldName(propertySelector);
    AddTrack<P>(fieldName, clip);
    yield return WaitReachTarget(fieldName);
  }

  public TweenAnim<T> PauseTrack(params Expression<Func<T, object>>[] propertySelectors)
  {
    propertySelectors.Select(p => GetTrack(p)).ToList().ForEach(t =>
    {
      t?.Pause();
    });
    return this;
  }
  public TweenAnim<T> ResumeTrack(params Expression<Func<T, object>>[] propertySelectors)
  {
    propertySelectors.Select(p => GetTrack(p)).ToList().ForEach(t =>
    {
      t?.Resume();
    });
    return this;
  }
  public TweenAnim<T> ResetTrack(params Expression<Func<T, object>>[] propertySelectors)
  {
    propertySelectors.Select(p => GetFieldName(p)).ToList().ForEach(n =>
    {
      if (_tweenTracks.ContainsKey(n))
      {
        _tweenTracks[n].Destroy();
        // We should just remove the track
        _tweenTracks.Remove(n);
      }
    });
    return this;
  }

  protected void TickTracks()
  {
    // _onTweenUpdate?.Invoke(Values);
    var newValue = new T();
    bool changed = false;
    foreach (var tweenTrack in _tweenTracks.Values)
    {
      var field = newValue.GetType().GetField(tweenTrack.fieldName);
      var currentValue = field.GetValue(Data.Value);
      if (tweenTrack.updateValue != null && (currentValue == null || !currentValue.Equals(tweenTrack.updateValue)))
      {
        object boxed = newValue;
        field.SetValue(boxed, tweenTrack.updateValue);
        newValue = (T)boxed;
        changed = true;
      }
      else
      {
        object boxed = newValue;
        field.SetValue(boxed, currentValue);
        newValue = (T)boxed;
      }
    }

    if (changed)
    {
      Data.Value = newValue;
      _onUpdate?.Invoke();
    }

    foreach (var tweenTrack in _tweenTracks.Values)
    {
      tweenTrack.NextTick();
    }

    // reach callbacks
    foreach (var callbackInfo in _reachCallbacks.ToArray())
    {
      if (callbackInfo.fieldNames.All(fieldName =>
      {
        var track = GetTrack(fieldName);
        return track == null || track.IsReachTarget;
      }))
      {
        callbackInfo.callback.Invoke();
        _reachCallbacks.Remove(callbackInfo);
      }
    }

    bool isCompletedAll = _tweenTracks.Values.All(t => t.IsCompleted);
    if (isCompletedAll)
    {
      _loopTimes++;
      _onLoop?.Invoke();

      if (_loopTimes >= _loopCount && _loopCount >= 0)
      {
        Debug.Log("completed");
        OnComplete();
      }
      else
      {
        _elapsedTime = 0f;
        foreach (var tweenTrack in _tweenTracks.Values)
        {
          tweenTrack.ResetLoop();
        }

        if (_pingpong)
        {
          _reverse = !_reverse;
        }
      }
    }
    else
    {
      _elapsedTime += Time.deltaTime;
    }
  }

  public override void Update()
  {
    if (!CanUpdate())
      return;

    foreach (var tweenTrack in _tweenTracks.Values)
    {
      if (tweenTrack.IsValid && !tweenTrack.IsCompleted && !tweenTrack.IsPaused)
      {
        tweenTrack.Update();
      }
    }

    TickTracks();
  }

  public object[] Values
  {
    get
    {
      return _tweenTracks.Values.Select(t => t.updateValue).ToArray();
    }
  }

  public TweenAnim<T> SetOnChange(Action<T> action)
  {
    Data.valueChanged += action;
    return this;
  }

  public TweenAnim<T> SetOnTrackComplete<P>(Expression<Func<T, P>> propertySelector, TrackCompleteCallback cb)
  {
    return this;
  }

  public TweenAnim<T> TriggerReachTarget(Action callback, params Expression<Func<T, object>>[] propertySelectors)
  {
    _reachCallbacks.Add((callback, propertySelectors.Select(p => GetFieldName(p)).ToArray()));
    return this;
  }

  public async Task WaitForTrack<P>(Expression<Func<T, P>> propertySelector)
  {
    var track = GetTrack(propertySelector);
    if (track == null)
    {
      Debug.LogWarning($"No track found for property: {GetFieldName(propertySelector)}");
      return;
    }

    if (track.IsReachTarget)
      return;

    var tcs = new TaskCompletionSource<bool>();

    void OnReachTarget()
    {
      tcs.SetResult(true);
      track.reachTarget -= OnReachTarget;
    }

    void OnRemoved()
    {
      tcs.SetCanceled();
      track.removed -= OnRemoved;
    }

    track.reachTarget += OnReachTarget;
    track.removed += OnRemoved;
    await tcs.Task;
  }

  public WaitForTweenTrack<T> WaitReachTarget(params Expression<Func<T, object>>[] propertySelectors)
  {
    return new WaitForTweenTrack<T>(this, propertySelectors);
  }
  public WaitForTweenTrack<T> WaitReachTarget(params string[] fieldNames)
  {
    return new WaitForTweenTrack<T>(this, fieldNames);
  }

  public TweenAnim<T> BeginBatchChange(TweenTrackBatchChange change)
  {
    _batchChange = change;
    return this;
  }
  public TweenAnim<T> EndBatchChange()
  {
    _batchChange = null;
    return this;
  }
}
