
using System.Linq;
using UnityEngine;

public class AnimTest : MonoBehaviour
{
  [SerializeField] AnimData animData;
  [SerializeField] AnimData animData2;
  [SerializeField] AnimData animDataLayer;
  struct Feature
  {
    public AnimFeature feature;

    // public readonly bool Equals(Feature other)
    // {
    //   return feature.Equals(other.feature);
    // }
  }

  struct Feature2
  {
    public float a;
  }

  private TweenAnim<Feature> _tween;

  void Start()
  {
    _tween = gameObject.TweenAnim<Feature>();
    CrossFrom();
    // Invoke("CrossTo", 1);
  }

  void CrossFrom()
  {
    _tween
      .SetOnChange(TestFeature)
      .SetTrack(f => f.feature, Tween.CreateAnimTrackClip(animData, 0, 0, loopCount: -1, pingpong: true, speed: 1f))
      .Start();
  }

  void Update()
  {
    if (Input.GetKeyDown(KeyCode.Space))
    {
      _tween.SetTrack(f => f.feature, Tween.CreateAnimTrackClip(animDataLayer, 1, 0, speed: 1f));
    }
  }

  void CrossTo()
  {
    _tween
      .BeginBatchChange(new() {
          crossDuration = 0.5f,
          crossEaseType = EaseType.EaseInQuad
       })
      .SetTrack(f => f.feature, Tween.CreateAnimTrackClip(animData2))
      .Start();
  }

  void TestFeature(Feature feature)
  {
    AnimHelper.AnimFeature(transform, feature.feature);
  }
}