
using Unity.VisualScripting;
using UnityEditor;
using UnityEngine;

public class SnakeController2 : MonoBehaviour
{
  struct Feature
  {
    public AnimFeature anim;
    public Vector2 lookDirection;
  }

  [SerializeField] AnimData idle;
  [SerializeField] AnimData aim;
  [SerializeField] AnimData mouthOpen;
  [SerializeField] AnimData mouthOpenMax;
  [SerializeField] AnimData eat;
  [SerializeField] float widthMultiplier;
  [SerializeField] float lookSpeed = 50f;
  [SerializeField] float eatDetectSqrDistance = 1f;

  public Transform root;
  public Transform neck;
  public Transform head;
  public Transform eye;
  public Transform mouth;
  public float rootTangent;
  public float neckTangent;
  public int bodyPrecision = 20;

  private GameObject target;

  private Quaternion _defaultHeadRotation;
  private Vector2 _defaultLookDirection;

  private LineRenderer _lineRenderer;
  private TweenAnim<Feature> _tween;

  private bool detectEat = false;

  private (Vector3, Vector3, Vector3, Vector3) beizierControlPoints => (
    root.position, root.position + root.right * rootTangent, neck.position - neck.right * neckTangent, neck.position
  );

  void OnDrawGizmos()
  {
    Gizmos.color = Color.green;
    Gizmos.DrawSphere(root.position, 0.2f);

    var startTangent = root.position + root.right * rootTangent;
    var endTangent = neck.position - neck.right * neckTangent;

    Handles.DrawDottedLine(root.position, startTangent, rootTangent);
    Handles.DrawDottedLine(neck.position, endTangent, neckTangent);

    Handles.DrawBezier(root.position, neck.position, startTangent, endTangent, Color.red, null, 5);
  }

  void DoFeature(Feature f)
  {
    // anim
    transform.AnimFeature(f.anim);

    // look target
    if (_tween.HasTrack(t => t.lookDirection))
    {
      var rotation = Quaternion.FromToRotation(_defaultLookDirection, f.lookDirection);
      head.rotation = _defaultHeadRotation * rotation;
    }

    // Draw body
    _lineRenderer.widthMultiplier = widthMultiplier;
    _lineRenderer.SetBeizierCurve(bodyPrecision, beizierControlPoints);
  }

  void Start()
  {
    _defaultLookDirection = eye.right;    
    _defaultHeadRotation = head.rotation;

    _lineRenderer = GetComponent<LineRenderer>();
    _tween = gameObject.TweenAnim<Feature>();

    _tween
      .SetOnChange(DoFeature)
      .BeginBatchChange(new () { loopCount = -1, pingpong = true })
      .SetTrack(f => f.anim, Tween.CreateAnimTrackClip(idle))
      .EndBatchChange()
      .Start();
  }

  void Update()
  {
    if (detectEat && target != null)
    {
      if (Vector2.SqrMagnitude(target.transform.position - mouth.position) < eatDetectSqrDistance)
      {
        Eat();
      }
    }
  }

  public void OnEnterDisturb(GameObject other, OverlapCircle area)
  {
    OnTargetChange(other, area);
  }

  public void OnExitDisturb(GameObject other, OverlapCircle area)
  {
    OnTargetChange(other, area);
  }

  private void OnTargetChange(GameObject other, OverlapCircle area)
  {
    var newTarget = area.GetOldestStayObject();
    if (newTarget == target)
      return;

    target = newTarget;

    _tween.SetTrack(t => t.lookDirection, Tween.CreateSpeedTrackClip(
      () => {
        if (target != null)
          return (Vector2)(target.transform.position - eye.position);
        return _defaultLookDirection;
      },
      () => lookSpeed,
      () => (Vector2)eye.right,
      interpolateFunc: Vector2Ext.SlerpTowards,
      compareFunc: Vector2Ext.AngleEqual
    ));
  }

  void Attack()
  {
    if (target != null)
    {
      // EditorApplication.isPaused = true;
      var constrain = new ChainMoveConstrain(transform, new () { effector = mouth, chainLength = 3});
      var (fromAnimData, toAnimData) = constrain.MoveTo(target.transform.position);

      detectEat = true;

      _tween
        .ResetTrack(t => t.lookDirection)
        .SetTrack(t => t.anim, Tween.CreateAnimTrackClip(fromAnimData, 0))
        .SetTrack(t => t.anim, Tween.CreateAnimTrackClip(toAnimData, 0, crossDuration: mouthOpenMax.duration))
        .SetTrack(t => t.anim, Tween.CreateAnimTrackClip(mouthOpenMax, 1))
        .TriggerReachTarget(() => {
          Debug.Log("eat completed");
        }, f => f.anim);
    }
  }

  void Eat()
  {
    if (!detectEat)
      return;

    _tween.SetTrack(t => t.anim, Tween.CreateAnimTrackClip(eat, 1));
    detectEat = false;
  }

  public void OnEnterAttackRange(GameObject other, OverlapCircle area)
  {
    // _tween.SetTrack(f => f.anim, Tween.CreateAnimTrackChange(idle));
    _tween
      .SetTrack(t => t.anim, Tween.CreateAnimTrackClip(aim, 0, crossDuration: 0.5f))
      .SetTrack(t => t.anim, Tween.CreateAnimTrackClip(mouthOpen, 1, crossDuration: 0.5f))
      .TriggerReachTarget(() => {
        Attack();
      }, f => f.lookDirection, f => f.anim);
  }

  public void OnExitAttackRange(GameObject other, OverlapCircle area)
  {
    // EditorApplication.isPaused = true;
    _tween
      .SetTrack(t => t.anim, Tween.CreateAnimTrackClip(idle, 0, crossDuration: 1f, loopCount: -1, pingpong: true))
      // .SetTrack(t => t.anim, Tween.CreateAnimTrackChange(mouthOpen, 1, reverse: true, crossDuration: 0.5f));
      ;
  }

}
