
using UnityEngine;

public static class MathfExt
{
  // 类似Godot的wrapf，将value循环到[min, max)范围
  public static float Wrap(float value, float min, float max)
  {
    float range = max - min;
    return min + Mathf.Repeat(value - min, range);
  }

  public static float ClampAngle(float value, float angle1, float angle2)
  {
    if ((value <= angle1 && value >= angle2) || (value >= angle1 && value <= angle2))
      return value;

    var diffAngle1 = Mathf.Abs(Mathf.DeltaAngle(value, angle1));
    var diffAngle2 = Mathf.Abs(Mathf.DeltaAngle(value, angle2));

    if (diffAngle1 <= diffAngle2)
      return angle1;

    return angle2;
  }

  public static Vector3 AngleToDirection(float angle)
  {
    return Quaternion.AngleAxis(angle, Vector3.forward) * Vector2.right;
  }

  public static float DirectionToAngle(Vector2 dir)
  {
    return Vector2.SignedAngle(Vector2.right, dir);
  }

  public static Quaternion Look(float angle)
  {
    return Quaternion.AngleAxis(angle, Vector3.forward);
  }

  public static Quaternion Look(Vector2 dir)
  {
    return Quaternion.FromToRotation(Vector2.right, dir);
  }

  public static float GetClosestEquivalentAngle(float a, float b)
  {
    // 计算b与a的原始差值
    float rawDiff = b - a;

    // 计算等效的差值（模360）
    float normalizedDiff = Mathf.Repeat(rawDiff + 180f, 360f) - 180f;

    // 返回最接近的等效角度
    return a + normalizedDiff;
  }

  public static float Remap(float value, float fromMin, float fromMax, float toMin, float toMax)
  {
    if (fromMin == fromMax)
      return toMin;

    float t = (value - fromMin) / (fromMax - fromMin);
    return Mathf.Lerp(toMin, toMax, t);
  }
}