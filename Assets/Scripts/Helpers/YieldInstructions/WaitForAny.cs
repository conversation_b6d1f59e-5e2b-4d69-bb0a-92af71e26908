
using UnityEngine;

public class WaitForAny : CustomYieldInstruction
{
    private CustomYieldInstruction[] instructions;

    public WaitForAny(params CustomYieldInstruction[] instructions)
    {
        this.instructions = instructions;
    }

    public override bool keepWaiting
    {
        get
        {
            foreach (var instruction in instructions)
            {
                if (!instruction.keepWaiting)
                {
                    return false;
                }
            }
            return true;
        }
    }
}