using System;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using UnityEditor;
using UnityEngine;

[RequireComponent(typeof(LineRenderer))]
public class SnakeController : MonoBehaviour
{
  class Feature
  {
    public float angle;
    public float lengthOffset;
    public float amplitude;
    public float testValue;
    public Vector2 lookDirection;
    public AnimFeature animFeature;
  }

  public Transform animRoot;
  public Transform root;
  public Transform head;
  public Transform target;
  public Transform eye;
  [SerializeField] float width = 1f;
  [SerializeField] int precision = 50;

  [Header("reaction")]
  [SerializeField] LayerMask playerLayerMask;
  [SerializeField] float attackDistance = 10f;

  [Header("Facial")]
  public Vector2 mouthOpenRange;
  [SerializeField] AnimData mouthOpenAnim;

  [Header("Idle Tween Settings")]
  [SerializeField] float idleLengthOffset = 1f;
  [SerializeField] float idleAmplitude = 1f;
  [SerializeField] Vector2 idleAngleRange = new(45f, 135f);
  [SerializeField] AnimationCurve lengthCurve;
  [SerializeField] AnimationCurve amplitudeCurve;
  [SerializeField] float lookSpeed = 50f;

  [SerializeField] float idleDuration = 2f;
  [Header("Aim Tween Settings")]
  [SerializeField] float aimDuration = .5f;
  [SerializeField] float aimAmplitude = 1.2f;
  [SerializeField] float aimAngle = 135f;
  [SerializeField] float aimLengthOffset = -1f;

  private LineRenderer lineRenderer;
  private float targetLength;

  private TweenAnim<Feature> _tweenBody;

  private Vector2 defaultLookDirection;

  void OnDrawGizmos()
  {
    Gizmos.color = Color.green;
    Gizmos.DrawSphere(root.position, 0.2f);
    Gizmos.color = Color.yellow;
    Gizmos.DrawSphere(head.position, 0.2f);

    GizmosExt.DrawLine((Vector2)root.position + Vector2.right * mouthOpenRange.x, (Vector2)root.position + Vector2.right * mouthOpenRange.y, Color.yellow);
  }

  void Start()
  {
    targetLength = Vector2.Distance(root.position, head.position);

    lineRenderer = GetComponent<LineRenderer>();
    lineRenderer.widthMultiplier = width;

    defaultLookDirection = eye.right;

    _tweenBody = gameObject.TweenAnim<Feature>();

    _tweenBody.Data.valueChanged += TestChange;

    Idle(false);

    _tweenBody
      .SetOnChange(UpdatePoints)
      .Start();
  }

  void TestChange(Feature feature)
  {
    animRoot.AnimFeature(feature.animFeature);
  }

  void Log(Feature feature)
  {
    Debug.Log(feature.testValue);
  }

  void Idle(bool test = false)
  {
    if (test)
    {
      _tweenBody.Data.valueChanged += Log;
    }

    _tweenBody
      .BeginBatchChange(new () { duration  = idleDuration, loopCount = -1, pingpong = true })
      .SetTrack(t => t.angle, Tween.CreateTimeTrackClip(new[] { idleAngleRange.x, idleAngleRange.y }))
      .SetTrack(t => t.lengthOffset, Tween.CreateTimeTrackClip<float>(Tween.FloatIdentity * 1, curve: lengthCurve))
      .SetTrack(t => t.amplitude, Tween.CreateTimeTrackClip<float>(Tween.FloatIdentity * 1, curve: amplitudeCurve))
      .SetTrack(t => t.testValue, Tween.CreateTimeTrackClip<float>(Tween.FloatIdentity * 1))
      // .SetFreeAnimTrack(t => t.animFeature, Tween.CreateFreeAnimTrackChange(animData, () => GetMouseRangePercent()))
      .SetTrack(t => t.lookDirection, Tween.CreateSpeedTrackClip(
        () => (Vector2)(target.position - eye.position),
        () => lookSpeed,
        () => defaultLookDirection,
        interpolateFunc: Vector2Ext.SlerpTowards
      ))
      .EndBatchChange()
      .Rewind();
  }

  void Aim()
  {
    _tweenBody.Data.valueChanged -= Log;
    _tweenBody.Data.valueChanged += Log;

    _tweenBody
      .BeginBatchChange(new() { duration = aimDuration })
      .SetTrack(t => t.angle, Tween.CreateTimeTrackClip(aimAngle))
      .SetTrack(t => t.lengthOffset, Tween.CreateTimeTrackClip(aimLengthOffset))
      .SetTrack(t => t.amplitude, Tween.CreateTimeTrackClip(aimAmplitude))
      .SetTrack(t => t.testValue, Tween.CreateTimeTrackClip<float>(Tween.FloatIdentity * 1))
      .SetTrack(t => t.animFeature, Tween.CreateAnimTrackClip(mouthOpenAnim))
      .EndBatchChange()
      .Rewind();
  }

  void Update()
  {
    // _tweenBody
    //   .SetLoop(1)
    //   .SetTrack(t => t.angle, Tween.CreateTimeTrackChange(aimDuration, aimAngle))
    //   .SetTrack(t => t.lengthOffset, Tween.CreateTimeTrackChange(aimDuration, aimLengthOffset))
    //   .SetTrack(t => t.amplitude, Tween.CreateTimeTrackChange(aimDuration, aimAmplitude))
    //   .Rewind();
  }

  // float GetMouseRangePercent()
  // {
  //   return Mathf.Clamp01(1f - (target.position.x - mouthOpenRange.x) / (mouthOpenRange.y - mouthOpenRange.x));
  // }

  public void OnEnterAttackRange()
  {
    Aim();
  }

  public void OnExitAttackRange()
  {
    _tweenBody.SetTrack(t => t.animFeature, Tween.CreateAnimTrackClip(mouthOpenAnim, 0, 0, 0.5f, reverse: true));
    Idle(true);
  }

  void UpdatePoints(Feature feature)
  {
    var dir = MathfExt.AngleToDirection(feature.angle);
    var length = targetLength + feature.lengthOffset;

    head.position = root.position + dir * length;

    // do
    int pointCount = GetPointCount(length);
    var points = LineRendExt.MakeCurve(root.position, dir, length, pointCount, feature.amplitude, EaseType.EaseOutQuad);

    var last = points.Length - 1;
    var headDir = points[last] - points[last - 1];

    var rotation = Quaternion.FromToRotation(head.right, headDir);
    head.rotation *= rotation;

    eye.parent.rotation *= Quaternion.Inverse(rotation);
    eye.parent.rotation *= Quaternion.FromToRotation(eye.right, feature.lookDirection);


    lineRenderer.widthMultiplier = width;
    lineRenderer.positionCount = pointCount;
    lineRenderer.SetPositions(points);
  }

  int GetPointCount(float length)
  {
    return precision;
    // return Mathf.CeilToInt(length * 1.5f);
  }
}
